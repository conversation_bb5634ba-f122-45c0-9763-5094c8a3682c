<template>
  <div>
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="报告人" prop="reporterId">
        <el-select v-model="queryParams.reporterId" placeholder="请选择报告人" clearable style="width: 200px">
          <el-option
            v-for="user in userOptions"
            :key="user.userId"
            :label="user.nickName"
            :value="user.userId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="报告日期" prop="reportDate">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="是否饱和" prop="isSaturated" v-if="reportType === 'weekly'">
        <el-select v-model="queryParams.isSaturated" placeholder="请选择是否饱和" clearable style="width: 200px">
          <el-option label="饱和" value="1" />
          <el-option label="不饱和" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >{{ addButtonText }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="reportList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="报告人" align="center">
        <template #default="scope">
          <span>{{ getUserNameById(scope.row.reporterId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报告日期" align="center" prop="reportDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.reportDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="summaryLabel" align="center" prop="summary" :show-overflow-tooltip="true" />
      <el-table-column :label="planLabel" align="center" prop="plan" :show-overflow-tooltip="true" />
      <el-table-column label="是否饱和" align="center" prop="isSaturated" v-if="reportType === 'weekly'">
        <template #default="scope">
          <dict-tag :options="saturatedOptions" :value="scope.row.isSaturated"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改周报月报对话框 -->
    <report-form
      ref="reportFormRef"
      :title="title"
      :open="open"
      :report-type="reportType"
      @cancel="cancel"
      @submit="submitForm"
    />

    <!-- 查看周报月报详情对话框 -->
    <report-detail
      ref="reportDetailRef"
      :title="detailTitle"
      :open="openDetail"
      :report-type="reportType"
      @cancel="cancelDetail"
    />
  </div>
</template>

<script setup>
import { pageReport, getReport, addReport, updateReport, delReport } from "@/api/report"
import { listUser, getUser } from "@/api/system/user"
import ReportForm from "./ReportForm.vue"
import ReportDetail from "./ReportDetail.vue"

const props = defineProps({
  reportType: {
    type: String,
    required: true,
    validator: (value) => ['weekly', 'monthly'].includes(value)
  }
})

const { proxy } = getCurrentInstance()
const { sys_normal_disable, sys_yes_no } = proxy.useDict('sys_normal_disable', 'sys_yes_no')

const reportList = ref([])
const open = ref(false)
const openDetail = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const detailTitle = ref("")
const dateRange = ref([])
const userOptions = ref([])

// 饱和状态选项
const saturatedOptions = ref([
  { label: '饱和', value: '1' },
  { label: '不饱和', value: '0' }
])

// 用户ID到用户名称的映射
const userIdToName = ref({})

// 计算属性：根据报告类型动态设置标签
const summaryLabel = computed(() => {
  return props.reportType === 'weekly' ? '本周总结' : '本月总结'
})

const planLabel = computed(() => {
  return props.reportType === 'weekly' ? '下周计划' : '下月计划'
})

const addButtonText = computed(() => {
  return props.reportType === 'weekly' ? '写周报' : '写月报'
})

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    reportType: props.reportType,
    reporterId: null,
    reportDateStart: null,
    reportDateEnd: null,
    isSaturated: null
  }
})

const { queryParams } = toRefs(data)

/** 根据用户ID获取用户名称 */
function getUserNameById(userId) {
  if (!userId) return "";
  return userIdToName.value[userId] || userId;
}

/** 加载用户名称 */
function loadUserNames() {
  // 收集所有用户ID
  const userIds = new Set();
  reportList.value.forEach(report => {
    if (report.reporterId) userIds.add(report.reporterId);
  });

  // 获取用户信息
  userIds.forEach(userId => {
    if (!userIdToName.value[userId]) {
      getUser(userId).then(response => {
        if (response.data) {
          userIdToName.value[userId] = response.data.nickName;
        }
      }).catch(() => {
        // 如果获取用户信息失败，使用用户ID作为显示名称
        userIdToName.value[userId] = `用户${userId}`;
      });
    }
  });
}

/** 查询周报月报列表 */
function getList() {
  loading.value = true
  queryParams.value.reportDateStart = dateRange.value ? dateRange.value[0] : null
  queryParams.value.reportDateEnd = dateRange.value ? dateRange.value[1] : null

  pageReport(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    reportList.value = response.data.rows
    total.value = response.data.total
    loading.value = false
    // 获取所有报告人的用户信息
    loadUserNames()
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 取消查看按钮 */
function cancelDetail() {
  openDetail.value = false
}

/** 表单重置 */
function reset() {
  proxy.$refs["reportFormRef"]?.reset()
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.reportId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = `${props.reportType === 'weekly' ? '写周报' : '写月报'}`
}

/** 查看按钮操作 */
function handleView(row) {
  const reportId = row.reportId
  getReport(reportId).then(response => {
    proxy.$refs["reportDetailRef"].setFormData(response.data)
    openDetail.value = true
    detailTitle.value = `查看${props.reportType === 'weekly' ? '周报' : '月报'}`
  })
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const reportId = row.reportId || ids.value
  getReport(reportId).then(response => {
    proxy.$refs["reportFormRef"].setFormData(response.data)
    open.value = true
    title.value = `修改${props.reportType === 'weekly' ? '周报' : '月报'}`
  })
}

/** 提交按钮 */
function submitForm(formData) {
  if (formData.reportId != undefined) {
    updateReport(formData).then(response => {
      proxy.$modal.msgSuccess("修改成功")
      open.value = false
      getList()
    })
  } else {
    addReport(formData).then(response => {
      proxy.$modal.msgSuccess("新增成功")
      open.value = false
      getList()
    })
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const reportIds = row.reportId || ids.value
  proxy.$modal.confirm('是否确认删除选中的数据项？').then(function() {
    return delReport(reportIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 获取用户列表 */
function getUserList() {
  listUser().then(response => {
    userOptions.value = response.data.rows || []
    // 更新用户ID到名称的映射
    if (response.data.rows) {
      response.data.rows.forEach(user => {
        userIdToName.value[user.userId] = user.nickName;
      });
    }
  }).catch(error => {
    console.error('获取用户列表失败:', error);
  })
}

// 暴露方法给父组件
defineExpose({
  getList
})

onMounted(() => {
  getList()
  getUserList()
})
</script>
